import { registerAs } from '@nestjs/config';

export interface DatabaseConfig {
  type: 'mysql';
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  charset?: string;
  timezone?: string;
  ssl: boolean;
}

export interface CacheConfig {
  host: string;
  port: number;
  password?: string;
  ttl: number;
}

export interface BindingServiceConfig {
  database: DatabaseConfig;
  cache: CacheConfig;
}

export default registerAs('binding', (): BindingServiceConfig => ({
  database: {
    type: 'mysql',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_DATABASE || 'ir_engine_binding',
    charset: 'utf8mb4',
    timezone: '+08:00',
    ssl: false,
  },
  cache: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    ttl: parseInt(process.env.CACHE_TTL || '3600'),
  },
}));
