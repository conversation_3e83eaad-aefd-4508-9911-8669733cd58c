import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

export interface ServiceInstance {
  id: string;
  name: string;
  address: string;
  port: number;
  health: boolean;
  metadata: Record<string, any>;
  lastSeen: Date;
}

@Injectable()
export class ServiceDiscoveryService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServiceDiscoveryService.name);
  private readonly httpClient: AxiosInstance;
  private readonly services = new Map<string, ServiceInstance[]>();
  private readonly serviceRegistryUrl: string;
  private discoveryInterval: ReturnType<typeof setInterval>;

  constructor(private readonly configService: ConfigService) {
    this.serviceRegistryUrl = this.configService.get(
      'SERVICE_REGISTRY_URL',
      'http://localhost:8010'
    );

    this.httpClient = axios.create({
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async onModuleInit() {
    await this.initializeStaticServices();
    await this.discoverServices();
    this.startPeriodicDiscovery();
    this.logger.log('服务发现初始化完成');
  }

  onModuleDestroy() {
    if (this.discoveryInterval) {
      clearInterval(this.discoveryInterval);
    }
    this.logger.log('服务发现已关闭');
  }

  /**
   * 获取服务实例列表
   */
  async getServiceInstances(serviceName: string): Promise<ServiceInstance[]> {
    const instances = this.services.get(serviceName) || [];
    
    // 过滤健康的实例
    return instances.filter(instance => instance.health);
  }

  /**
   * 获取所有服务
   */
  getAllServices(): Map<string, ServiceInstance[]> {
    return new Map(this.services);
  }

  /**
   * 注册服务实例
   */
  async registerService(instance: Omit<ServiceInstance, 'id' | 'lastSeen'>): Promise<void> {
    try {
      const response = await this.httpClient.post(`${this.serviceRegistryUrl}/registry/register`, {
        name: instance.name,
        address: instance.address,
        port: instance.port,
        health: instance.health,
        metadata: instance.metadata,
      });

      if (response.status === 201) {
        this.logger.log(`服务注册成功: ${instance.name}@${instance.address}:${instance.port}`);
      }
    } catch (error) {
      this.logger.error(`服务注册失败: ${instance.name}`, error.message);
    }
  }

  /**
   * 注销服务实例
   */
  async deregisterService(serviceName: string, instanceId: string): Promise<void> {
    try {
      await this.httpClient.delete(`${this.serviceRegistryUrl}/registry/deregister/${instanceId}`);
      
      // 从本地缓存中移除
      const instances = this.services.get(serviceName) || [];
      const filteredInstances = instances.filter(instance => instance.id !== instanceId);
      this.services.set(serviceName, filteredInstances);
      
      this.logger.log(`服务注销成功: ${serviceName}/${instanceId}`);
    } catch (error) {
      this.logger.error(`服务注销失败: ${serviceName}/${instanceId}`, error.message);
    }
  }

  /**
   * 更新服务健康状态
   */
  updateServiceHealth(serviceName: string, instanceId: string, health: boolean): void {
    const instances = this.services.get(serviceName) || [];
    const instance = instances.find(inst => inst.id === instanceId);
    
    if (instance) {
      instance.health = health;
      instance.lastSeen = new Date();
      this.logger.debug(`更新服务健康状态: ${serviceName}/${instanceId} -> ${health}`);
    }
  }

  /**
   * 获取服务统计信息
   */
  getServiceStats(): {
    totalServices: number;
    totalInstances: number;
    healthyInstances: number;
    serviceDetails: Array<{
      name: string;
      instanceCount: number;
      healthyCount: number;
    }>;
  } {
    let totalInstances = 0;
    let healthyInstances = 0;
    const serviceDetails: Array<{
      name: string;
      instanceCount: number;
      healthyCount: number;
    }> = [];

    for (const [serviceName, instances] of this.services) {
      const instanceCount = instances.length;
      const healthyCount = instances.filter(inst => inst.health).length;
      
      totalInstances += instanceCount;
      healthyInstances += healthyCount;
      
      serviceDetails.push({
        name: serviceName,
        instanceCount,
        healthyCount,
      });
    }

    return {
      totalServices: this.services.size,
      totalInstances,
      healthyInstances,
      serviceDetails,
    };
  }

  /**
   * 初始化静态服务配置
   */
  private async initializeStaticServices(): Promise<void> {
    const staticServices = [
      {
        name: 'user-service',
        address: this.configService.get('USER_SERVICE_HOST', 'localhost'),
        port: this.configService.get('USER_SERVICE_PORT', 8001),
        health: true,
        metadata: { version: '1.0.0', type: 'http' },
      },
      {
        name: 'project-service',
        address: this.configService.get('PROJECT_SERVICE_HOST', 'localhost'),
        port: this.configService.get('PROJECT_SERVICE_PORT', 8006),
        health: true,
        metadata: { version: '1.0.0', type: 'http' },
      },
      {
        name: 'asset-library-service',
        address: this.configService.get('ASSET_LIBRARY_SERVICE_HOST', 'localhost'),
        port: this.configService.get('ASSET_LIBRARY_SERVICE_PORT', 8003),
        health: true,
        metadata: { version: '1.0.0', type: 'http' },
      },
      {
        name: 'scene-template-service',
        address: this.configService.get('SCENE_TEMPLATE_SERVICE_HOST', 'localhost'),
        port: this.configService.get('SCENE_TEMPLATE_SERVICE_PORT', 8004),
        health: true,
        metadata: { version: '1.0.0', type: 'http' },
      },
      {
        name: 'ai-model-service',
        address: this.configService.get('AI_MODEL_SERVICE_HOST', 'localhost'),
        port: this.configService.get('AI_MODEL_SERVICE_PORT', 8002),
        health: true,
        metadata: { version: '1.0.0', type: 'http' },
      },
      {
        name: 'scene-generation-service',
        address: this.configService.get('SCENE_GENERATION_SERVICE_HOST', 'localhost'),
        port: this.configService.get('SCENE_GENERATION_SERVICE_PORT', 8005),
        health: true,
        metadata: { version: '1.0.0', type: 'http' },
      },
      {
        name: 'render-service',
        address: this.configService.get('RENDER_SERVICE_HOST', 'localhost'),
        port: this.configService.get('RENDER_SERVICE_PORT', 8007),
        health: true,
        metadata: { version: '1.0.0', type: 'http' },
      },
      {
        name: 'knowledge-service',
        address: this.configService.get('KNOWLEDGE_SERVICE_HOST', 'localhost'),
        port: this.configService.get('KNOWLEDGE_SERVICE_PORT', 8008),
        health: true,
        metadata: { version: '1.0.0', type: 'http' },
      },
      {
        name: 'rag-engine',
        address: this.configService.get('RAG_ENGINE_HOST', 'localhost'),
        port: this.configService.get('RAG_ENGINE_PORT', 8009),
        health: true,
        metadata: { version: '1.0.0', type: 'http' },
      },
    ];

    for (const service of staticServices) {
      const instance: ServiceInstance = {
        id: `${service.name}-${service.address}-${service.port}`,
        name: service.name,
        address: service.address,
        port: service.port,
        health: service.health,
        metadata: service.metadata,
        lastSeen: new Date(),
      };

      const instances = this.services.get(service.name) || [];
      instances.push(instance);
      this.services.set(service.name, instances);
    }

    this.logger.log(`初始化了 ${staticServices.length} 个静态服务`);
  }

  /**
   * 从服务注册中心发现服务
   */
  private async discoverServices(): Promise<void> {
    try {
      const response = await this.httpClient.get(`${this.serviceRegistryUrl}/registry/services`);
      const discoveredServices = response.data;

      for (const service of discoveredServices) {
        const instance: ServiceInstance = {
          id: service.id,
          name: service.name,
          address: service.address,
          port: service.port,
          health: service.health,
          metadata: service.metadata || {},
          lastSeen: new Date(service.lastSeen),
        };

        const instances = this.services.get(service.name) || [];
        const existingIndex = instances.findIndex(inst => inst.id === instance.id);
        
        if (existingIndex >= 0) {
          instances[existingIndex] = instance;
        } else {
          instances.push(instance);
        }
        
        this.services.set(service.name, instances);
      }

      this.logger.debug(`发现了 ${discoveredServices.length} 个服务实例`);
    } catch (error) {
      this.logger.warn('从服务注册中心发现服务失败，使用静态配置', error.message);
    }
  }

  /**
   * 开始定期服务发现
   */
  private startPeriodicDiscovery(): void {
    const interval = this.configService.get('SERVICE_DISCOVERY_INTERVAL', 30000); // 30秒
    
    this.discoveryInterval = setInterval(async () => {
      await this.discoverServices();
      this.cleanupStaleServices();
    }, interval);

    this.logger.log(`开始定期服务发现，间隔: ${interval}ms`);
  }

  /**
   * 清理过期服务
   */
  private cleanupStaleServices(): void {
    const staleThreshold = this.configService.get('SERVICE_STALE_THRESHOLD', 120000); // 2分钟
    const now = new Date();

    for (const [serviceName, instances] of this.services) {
      const activeInstances = instances.filter(instance => {
        const timeSinceLastSeen = now.getTime() - instance.lastSeen.getTime();
        return timeSinceLastSeen < staleThreshold;
      });

      if (activeInstances.length !== instances.length) {
        this.services.set(serviceName, activeInstances);
        this.logger.debug(`清理过期服务实例: ${serviceName}, 剩余: ${activeInstances.length}`);
      }
    }
  }
}
