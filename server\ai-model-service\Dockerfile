# 使用官方 Node.js 运行时作为基础镜像
FROM node:22-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制AI模型服务代码
COPY ai-model-service/package*.json ./ai-model-service/
WORKDIR /app/ai-model-service

# 安装依赖
RUN npm ci --only=production

# 复制AI模型服务源代码
COPY ai-model-service/ ./

# 构建应用
RUN npm run build

FROM node:22-alpine

WORKDIR /app

COPY --from=builder /app/ai-model-service/package*.json ./
COPY --from=builder /app/ai-model-service/dist ./dist

RUN npm install --only=production

# 暴露端口
EXPOSE 3008 3018

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# 切换到非 root 用户
USER nestjs

# 启动应用
CMD ["node", "dist/main.js"]
