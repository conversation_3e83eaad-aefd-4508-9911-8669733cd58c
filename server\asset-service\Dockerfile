FROM node:22-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . ./

# 构建应用
RUN npm run build

FROM node:22-alpine

WORKDIR /app

# 复制package文件
COPY --from=builder /app/package*.json ./

# 安装生产依赖
RUN npm ci --only=production && npm cache clean --force

# 复制构建产物
COPY --from=builder /app/dist ./dist

# 创建上传目录
RUN mkdir -p /app/uploads/models /app/uploads/textures /app/uploads/audio /app/uploads/other

EXPOSE 3003 4003

CMD ["node", "dist/main.js"]
